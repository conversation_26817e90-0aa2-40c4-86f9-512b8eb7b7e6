import {
  pgTable,
  smallserial,
  bigserial,
  text,
  integer,
  bigint,
  timestamp,
  primaryKey,
  index,
  uniqueIndex,
} from 'drizzle-orm/pg-core';

/* ----------  lookup: platforms -------------------------------- */
export const portalPlatforms = pgTable(
  'portal_platforms',
  {
    id: smallserial('id').primaryKey(),
    name: text('name').notNull(),
  },
  (t) => [uniqueIndex('portal_platforms_name_uq').on(t.name)],
);

/* ----------  global creators ---------------------------------- */
export const portalGlobalCreators = pgTable('portal_global_creators', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  name: text('name'),
  website: text('website'),
  note: text('note'),
});

/* ----------  platform creators -------------------------------- */
export const portalCreators = pgTable(
  'portal_creators',
  {
    id: bigserial('id', { mode: 'number' }).primaryKey(),
    platformId: smallserial('platform_id')
      .references(() => portalPlatforms.id, { onDelete: 'cascade' })
      .notNull(),
    platformCreatorId: text('platform_creator_id').notNull(),
    handle: text('handle'),
    displayName: text('display_name'),
    profileUrl: text('profile_url'),
    avatarUrl: text('avatar_url'),
    bio: text('bio'),
    followerCount: bigint('follower_count', { mode: 'number' }),
    globalCreatorId: bigint('global_creator_id', {
      mode: 'number',
    }).references(() => portalGlobalCreators.id, { onDelete: 'set null' }),
    // New fields from TiktokVideoAuthorSchema
    uid: text('uid'),
    awemeCount: bigint('aweme_count', { mode: 'number' }),
    createTime: bigint('create_time', { mode: 'number' }),
    region: text('region'),
    language: text('language'),
    userTags: text('user_tags'),
    youtubeChannelId: text('youtube_channel_id'),
    insId: text('ins_id'),
    twitterId: text('twitter_id'),
  },
  (t) => [
    uniqueIndex('portal_creators_platform_ext_uq').on(
      t.platformId,
      t.platformCreatorId,
    ),
  ],
);

/* ----------  videos ------------------------------------------- */
export const portalVideos = pgTable(
  'portal_videos',
  {
    id: bigserial('id', { mode: 'number' }).primaryKey(),
    platformId: smallserial('platform_id')
      .references(() => portalPlatforms.id, { onDelete: 'cascade' })
      .notNull(),
    platformVideoId: text('platform_video_id').notNull(),
    creatorId: bigserial('creator_id', { mode: 'number' })
      .references(() => portalCreators.id, { onDelete: 'cascade' })
      .notNull(),
    title: text('title'),
    description: text('description'),
    publishedAt: timestamp('published_at', { withTimezone: true }),
    durationSeconds: integer('duration_seconds'),
    language: text('language'),
    thumbnailUrl: text('thumbnail_url'),
    rawTags: text('raw_tags').array().notNull().default([]),
  },
  (t) => [
    uniqueIndex('portal_videos_platform_ext_uq').on(
      t.platformId,
      t.platformVideoId,
    ),
    index('portal_videos_published_idx').on(t.publishedAt.desc()),
  ],
);

/* ----------  video‑stats snapshots ---------------------------- */
export const portalVideoStats = pgTable(
  'portal_video_stats',
  {
    id: bigserial('id', { mode: 'number' }).primaryKey(),
    videoId: bigserial('video_id', { mode: 'number' })
      .references(() => portalVideos.id, { onDelete: 'cascade' })
      .notNull(),
    collectedAt: timestamp('collected_at', { withTimezone: true })
      .defaultNow()
      .notNull(),
    viewCount: bigint('view_count', { mode: 'number' }),
    likeCount: bigint('like_count', { mode: 'number' }),
    commentCount: bigint('comment_count', { mode: 'number' }),
    shareCount: bigint('share_count', { mode: 'number' }),
  },
  (t) => [
    uniqueIndex('portal_video_stats_snapshot_uq').on(t.videoId, t.collectedAt),
    index('portal_video_stats_video_time_idx').on(
      t.videoId,
      t.collectedAt.desc(),
    ),
  ],
);

/* ----------  tags ----------------------------------- */
export const portalTags = pgTable(
  'portal_tags',
  {
    id: bigserial('id', { mode: 'number' }).primaryKey(),
    tag: text('tag').notNull(),
  },
  (t) => [uniqueIndex('portal_tags_tag_uq').on(t.tag)],
);

/* ----------  link: videos ↔ tags ------------------------------ */
export const portalVideoTags = pgTable(
  'portal_video_tags',
  {
    videoId: bigserial('video_id', { mode: 'number' })
      .references(() => portalVideos.id, { onDelete: 'cascade' })
      .notNull(),
    tagId: bigserial('tag_id', { mode: 'number' })
      .references(() => portalTags.id, { onDelete: 'cascade' })
      .notNull(),
    rawToken: text('raw_token').notNull(),
  },
  (t) => [
    primaryKey({ columns: [t.tagId, t.rawToken] }),
    index('portal_video_tags_tag_idx').on(t.tagId),
  ],
);
