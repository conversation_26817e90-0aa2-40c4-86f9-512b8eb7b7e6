import { and, eq } from 'drizzle-orm';
import type { DatabaseInstance } from '../client';
import {
  portalVideos,
  portalVideoStats,
  portalTags,
  portalVideoTags,
  portalCreators,
  portalPlatforms,
} from '../schemas/agent_schema';
import { type TiktokVideoSchema } from '@repo/constants';

/**
 * Database service for handling video-related operations
 */
export class ScoutDb {
  constructor(private db: DatabaseInstance) {
    this.db = db;
  }

  /**
   * Find a video by platform and platform video ID
   */
  async findVideoByPlatformId(platformId: number, platformVideoId: string) {
    const videos = await this.db
      .select()
      .from(portalVideos)
      .where(
        and(
          eq(portalVideos.platformId, platformId),
          eq(portalVideos.platformVideoId, platformVideoId),
        ),
      );

    return videos.length > 0 ? videos[0] : null;
  }

  /**
   * Find a creator by platform and platform creator ID
   */
  async findCreatorByPlatformId(platformId: number, platformCreatorId: string) {
    const creators = await this.db
      .select()
      .from(portalCreators)
      .where(
        and(
          eq(portalCreators.platformId, platformId),
          eq(portalCreators.platformCreatorId, platformCreatorId),
        ),
      );

    return creators.length > 0 ? creators[0] : null;
  }

  /**
   * Find a platform by name
   */
  async findPlatformByName(name: string) {
    const platforms = await this.db
      .select()
      .from(portalPlatforms)
      .where(eq(portalPlatforms.name, name));

    return platforms.length > 0 ? platforms[0] : null;
  }

  /**
   * Create a new platform
   */
  async createPlatform(name: string) {
    const result = await this.db
      .insert(portalPlatforms)
      .values({
        name,
      })
      .returning({ id: portalPlatforms.id, name: portalPlatforms.name });

    return result[0];
  }

  /**
   * Create or update a creator
   */
  async createOrUpdateCreator(
    platformId: number,
    platformCreatorId: string,
    creatorData: {
      handle?: string;
      displayName?: string;
      profileUrl?: string;
      avatarUrl?: string;
      bio?: string;
      followerCount?: number;
      globalCreatorId?: number;
    },
  ) {
    const existingCreator = await this.findCreatorByPlatformId(
      platformId,
      platformCreatorId,
    );

    if (existingCreator) {
      // Update existing creator
      await this.db
        .update(portalCreators)
        .set(creatorData)
        .where(eq(portalCreators.id, existingCreator.id));

      return existingCreator.id;
    } else {
      // Create new creator
      const result = await this.db
        .insert(portalCreators)
        .values({
          platformId,
          platformCreatorId,
          ...creatorData,
        })
        .returning({ id: portalCreators.id });

      if (result[0]) {
        return result[0].id;
      } else {
        throw new Error('Failed to create creator');
      }
    }
  }

  /**
   * Create or update a video
   */
  async createOrUpdateVideo(
    platformId: number,
    platformVideoId: string,
    creatorId: number,
    videoData: {
      title?: string;
      description?: string;
      publishedAt?: Date;
      durationSeconds?: number;
      language?: string;
      thumbnailUrl?: string;
      rawTags?: string[];
    },
  ) {
    const existingVideo = await this.findVideoByPlatformId(
      platformId,
      platformVideoId,
    );

    if (existingVideo) {
      // Update existing video
      await this.db
        .update(portalVideos)
        .set(videoData)
        .where(eq(portalVideos.id, existingVideo.id));

      return existingVideo.id;
    } else {
      // Create new video
      const result = await this.db
        .insert(portalVideos)
        .values({
          platformId,
          platformVideoId,
          creatorId,
          ...videoData,
        })
        .returning({ id: portalVideos.id });
      if (result[0]) {
        return result[0].id;
      } else {
        throw new Error('Failed to create video');
      }
    }
  }

  /**
   * Create a new video stats snapshot
   */
  async createVideoStats(
    videoId: number,
    statsData: {
      viewCount?: number;
      likeCount?: number;
      commentCount?: number;
      shareCount?: number;
    },
  ) {
    await this.db.insert(portalVideoStats).values({
      videoId,
      ...statsData,
    });
  }

  /**
   * Find or create a tag
   */
  async findOrCreateTag(tag: string) {
    const existingTags = await this.db
      .select()
      .from(portalTags)
      .where(eq(portalTags.tag, tag));

    if (existingTags.length > 0 && existingTags[0]) {
      return existingTags[0].id;
    } else {
      const result = await this.db
        .insert(portalTags)
        .values({
          tag,
        })
        .returning({ id: portalTags.id });

      if (result[0]) {
        return result[0].id;
      } else {
        throw new Error('Failed to create tag');
      }
    }
  }

  /**
   * Associate a tag with a video
   */
  async associateVideoTag(videoId: number, tagId: number, rawToken: string) {
    try {
      await this.db.insert(portalVideoTags).values({
        videoId,
        tagId,
        rawToken,
      });
    } catch (error) {
      // Ignore duplicate key errors
      console.error('Tag association already exists or error:', error);
    }
  }

  /**
   * Process a TikTok video and update the database
   */
  async processTikTokVideo(video: TiktokVideoSchema) {
    try {
      // 1. Find platform ID for TikTok
      let platform = await this.findPlatformByName('tiktok');
      if (!platform) {
        console.warn('Platform[tiktok] not found in database, creating...');
        platform = await this.createPlatform('tiktok');
        console.log('Platform[tiktok] created successfully');
      }
      if (!platform) {
        throw new Error('Create platform failed');
      }

      // 2. Create or update creator
      const creatorId = await this.createOrUpdateCreator(
        platform.id,
        video.author.sec_uid,
        {
          handle: video.author.unique_id,
          displayName: video.author.nickname,
          bio: video.author.signature,
          followerCount: video.author.follower_count,
          avatarUrl: video.author.avatar_url,
        },
      );

      // 3. Create or update video
      // console.log(
      //   'Video data:',
      //   JSON.stringify({
      //     title: video.title?.substring(0, 20) + '...',
      //     publishedAt: video.publish_time,
      //     durationSeconds: video.duration,
      //     platformId: platform.id,
      //     creatorId: creatorId,
      //   }),
      // );
      const publishedAt = new Date(video.publish_time);
      const videoId = await this.createOrUpdateVideo(
        platform.id,
        video.video_id,
        creatorId,
        {
          title: video.title,
          description: video.description,
          publishedAt,
          durationSeconds: video.duration,
          language: video.author.language,
          thumbnailUrl: video.thumbnail_url,
          rawTags: video.hashtags,
        },
      );

      // 4. Create video stats snapshot
      await this.createVideoStats(videoId, {
        viewCount: video.view_count,
        likeCount: video.like_count,
        commentCount: video.comment_count,
        shareCount: video.share_count,
      });

      // TODO: 5. Process tags (tags normalization)
      // for (const hashtag of video.hashtags) {
      //   const tagId = await this.findOrCreateTag(hashtag);
      //   await this.associateVideoTag(videoId, tagId, hashtag);
      // }

      return videoId;
    } catch (error) {
      console.error('Error processing TikTok video:', error);
      throw error;
    }
  }
}

// Export a singleton instance
// export const scoutDbService = new ScoutDb();

export const createScoutDbService = (db: DatabaseInstance) => {
  return new ScoutDb(db);
};
