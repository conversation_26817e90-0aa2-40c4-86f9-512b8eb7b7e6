{"name": "@repo/db", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./client": {"types": "./dist/client.d.ts", "default": "./dist/client.js"}, "./schema": {"types": "./dist/schema.d.ts", "default": "./dist/schema.js"}, "./operations": {"types": "./dist/operations.d.ts", "default": "./dist/operations.js"}}, "scripts": {"build": "tsup", "dev": "tsup", "clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "push": "pnpm env:run drizzle-kit push", "studio": "pnpm env:run drizzle-kit studio", "typecheck": "tsc --build --noEmit --emitDeclarationOnly false", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "prettier": "@repo/prettier-config", "dependencies": {"@repo/constants": "workspace:*", "drizzle-orm": "catalog:", "drizzle-valibot": "catalog:", "pg": "catalog:", "valibot": "catalog:"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "catalog:", "@types/pg": "catalog:", "dotenv-cli": "catalog:", "drizzle-kit": "catalog:", "eslint": "catalog:", "shx": "catalog:", "tsup": "catalog:", "typescript": "catalog:"}}