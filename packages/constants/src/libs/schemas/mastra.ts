export interface WorkflowSnapshotState {
  value: Record<string, string>;
  context: {
    steps: Record<
      string,
      {
        status: 'success' | 'failed' | 'suspended' | 'waiting' | 'skipped';
        payload?: any;
        error?: string;
        output?: string;
      }
    >;
    triggerData: Record<string, any>;
    attempts: Record<string, number>;
  };
  activePaths: Array<{
    stepPath: string[];
    stepId: string;
    status: string;
  }>;
  suspendedPaths: Record<string, number[]>;
  runId: string;
  timestamp: number;
  childStates?: Record<string, WorkflowSnapshotState>;
  suspendedSteps?: Record<string, string>;
}
