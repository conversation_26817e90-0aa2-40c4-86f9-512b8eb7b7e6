import { userWorkflowService } from "@/services/workflow/userWorkflowService";

/**
 * Example of how to use the user workflow service
 */
async function runExample() {
  // Example user ID (this would come from your authentication system)
  const userId = "user_123";
  
  // Create a new workflow run for the user
  const { runId, start } = await userWorkflowService.createWorkflowRun(
    userId,
    "aiScriptWorkflow", // The name of the workflow to run
    { campaignDescription: "Create a TikTok campaign for a mobile game" }
  );
  
  console.log(`Created workflow run with ID: ${runId}`);
  
  // Start the workflow
  try {
    const result = await start();
    console.log("Workflow completed successfully:", result);
  } catch (error) {
    console.error("Workflow failed:", error);
  }
  
  // List all workflows for the user
  const userWorkflows = await userWorkflowService.getUserWorkflows(userId);
  console.log(`User has ${userWorkflows.length} workflows:`);
  
  for (const workflow of userWorkflows) {
    console.log(`- ${workflow.workflowName} (${workflow.status}): ${workflow.workflowRunId}`);
    
    // Get the workflow contexts
    const contexts = await userWorkflowService.getWorkflowContexts(workflow.workflowRunId);
    console.log(`  Has ${contexts.length} context entries`);
  }
}

// Run the example
runExample().catch(console.error);
