import { CoreMessage } from '@mastra/core';
// import { Step, Workflow } from '@mastra/core/workflows';
import { LegacyStep, LegacyWorkflow } from '@mastra/core/workflows/legacy';

import { z } from 'zod';
import { videoScouter } from '@/services/scouting/videoScouter';

const videoSearchSchema = z.object({
  keywords: z.array(z.string()),
});

const challengSearchResultSchema = z.object({
  challengeCount: z.number(),
});

export const aiScriptWorkflow = new LegacyWorkflow({
  name: 'ai-script',
  triggerSchema: z.object({
    campaignDescription: z.string(),
  }),
});

const stepOne = new LegacyStep({
  id: 'analyze-description',
  outputSchema: videoSearchSchema,
  execute: async ({ context, mastra }) => {
    const description = context.triggerData.campaignDescription;
    console.log('description', description);

    const campaignAnalyzer = mastra?.getAgent('campaignAnalyzer');
    if (!campaignAnalyzer) {
      throw new Error('Campaign analyzer agent not found');
    }

    const userDescription: CoreMessage = {
      role: 'user',
      content: description,
    };

    console.log('userDescription', userDescription);

    const resp = await campaignAnalyzer.generate([userDescription], {
      output: videoSearchSchema,
    });

    console.log('resp', resp);

    return resp.object;
  },
});

const stepTwo = new LegacyStep({
  id: 'find-best-challenges',
  inputSchema: videoSearchSchema,
  outputSchema: challengSearchResultSchema,
  execute: async ({ context, runId }) => {
    const { keywords } = context.inputData;
    console.log('Finding best challenges for keywords:', keywords);

    const bestChallenges = await videoScouter.findBestChallenges(
      keywords,
      runId,
      20, // Get at least 100 challenges
    );

    return {
      challengeCount: bestChallenges.length,
    };
  },
});

const stepThree = new LegacyStep({
  id: 'collect-good-videos',
  inputSchema: challengSearchResultSchema,
  execute: async ({ runId }) => {
    console.log('Collecting good videos from challenges');

    const result = await videoScouter.collectGoodVideos(
      runId,
      100, // Collect at least 500 videos
    );

    console.log('Collected', result.totalVideos, 'videos');
    console.log('Video ids:', result.processedVideoIds);

    return {
      videoCount: result.totalVideos,
      processedCount: result.processedVideoIds.length,
    };
  },
});

aiScriptWorkflow
  .step(stepOne)
  .then(stepTwo, {
    variables: {
      keywords: { step: stepOne, path: 'keywords' },
    },
  })
  .then(stepThree, {
    variables: {
      challengeCount: { step: stepTwo, path: 'challengeCount' },
    },
  })
  .commit();
