import { extract<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/utils';
import { createStep, createWorkflow } from '@mastra/core/workflows';
import { CoreMessage, TextPart } from 'ai';
import { z } from 'zod';
import { videoScouter } from '@/services/scouting/videoScouter';
import { TiktokVideoSchema, TiktokVideoAuthorSchema } from '@repo/constants';
// import { workflowDbService } from '@/services/index';

// Schema for the hashtag search results
const searchHashtagSchema = z.object({
  core: z.array(z.string()),
  adjacent: z.array(z.string()),
  rationale: z.string().optional(),
});

// Schema for creator filtering results
const creatorFilterSchema = z.object({
  qualified_kols: z.array(
    z.object({
      unique_id: z.string(),
      collect_reason: z.string(),
    }),
  ),
});

const workflowOutputSchema = z.object({
  desiredCreators: z.number(),
  scoutedCreators: z.number(),
  results: z.array(
    z.object({
      url: z.string().url(),
      reason: z.string(),
    }),
  ),
});

export const creatorScoutWorkflow = createWorkflow({
  id: 'creator-scout',
  inputSchema: z.object({
    targetCreatorDescription: z.string(),
  }),
  outputSchema: workflowOutputSchema,
});

// Step 1: Analyze the user's requirements and generate hashtags
const analyzeRequirementStep = createStep({
  id: 'analyzeRequirement',
  inputSchema: z.object({
    targetCreatorDescription: z.string(),
  }),
  resumeSchema: z.object({
    userInputMessage: z.string().optional(),
    messages: z.array(z.any()),
  }),
  outputSchema: z.object({
    core: z.array(z.string()),
    adjacent: z.array(z.string()),
    rationale: z.string().optional(),
    targetCreatorDescription: z.string(), // Pass through
    scoutGuidance: z.string().optional(), // Scout's refined guidance
  }),
  execute: async ({ inputData, resumeData, suspend, mastra }) => {
    console.log('=== STEP EXECUTION START ===');
    // console.log('inputData', inputData);
    // console.log('resumeData', resumeData);

    const scout = mastra?.getAgent('creatorHashtagScout');
    if (!scout) {
      throw new Error('Campaign analyzer agent not found');
    }

    // Initialize messages array
    let messages: CoreMessage[] = [];

    // Check if this is a resume (either resumeData exists OR inputData has been contaminated with resume data)
    const isResume = resumeData?.messages || (inputData as any).messages;

    if (isResume) {
      // If we have cached messages, use them (this is a resume)
      console.log('Found cached messages, resuming conversation');

      // Get messages from resumeData first, fallback to inputData if framework merged them
      const cachedMessages =
        resumeData?.messages || (inputData as any).messages;
      messages = [...cachedMessages] as CoreMessage[]; // Create a copy to avoid mutation

      // Get user input message from resumeData first, fallback to inputData
      const userInputMessage =
        resumeData?.userInputMessage || (inputData as any).userInputMessage;

      // Add the user's response from the resumed workflow
      if (userInputMessage) {
        console.log('user responded:', userInputMessage);
        const userResponse: CoreMessage = {
          role: 'user',
          content: userInputMessage,
        };
        messages.push(userResponse);
      }
    } else {
      // If no cached messages, start a new conversation
      console.log('No cached messages found, starting new conversation');
      const description = inputData.targetCreatorDescription;
      const userDescription: CoreMessage = {
        role: 'user',
        content: description,
      };
      messages.push(userDescription);
    }

    console.log('About to call agent with messages:', messages.length);
    // Generate a response from the agent
    const resp = await scout.generate(messages);
    // console.log('Agent response received');
    const assistantMessage = resp.response.messages[0];
    const content = (assistantMessage.content as Array<TextPart>)[0];

    let parsedResult;
    // Check if the response is in the expected format
    try {
      // Extract the first JSON object from the response
      parsedResult = extractFirstJson(content.text);
      console.log('assistant response:', content.text);
      // console.log('parsedResult', parsedResult);
    } catch (e) {
      // JSON parsing failed, continue to suspend
    }

    const parseResult = searchHashtagSchema.safeParse(parsedResult);
    if (parseResult.success) {
      // console.log('parseResult.data', parseResult.data);
      return {
        ...parseResult.data,
        targetCreatorDescription: inputData.targetCreatorDescription,
        scoutGuidance: parseResult.data.rationale || content.text, // Use rationale or full response as guidance
      };
    }

    // If not in expected format, add assistant message to conversation and suspend
    const updatedMessages = [...messages, assistantMessage];
    // console.log('appended messages', messages);
    // console.log('updatedMessages', updatedMessages);

    // console.log('before suspend, we are here');

    // Suspend and wait for user input
    // When resumed, the step will restart with the updated messages in resumeData
    await suspend({
      messages: updatedMessages,
      message: content,
    });

    // console.log('after suspend, we are here, returning empty hashtags');

    // This code should not execute in normal operation since suspend should restart the step
    // But we need to return something to satisfy TypeScript
    return {
      core: [],
      adjacent: [],
      targetCreatorDescription: inputData.targetCreatorDescription,
      scoutGuidance: 'No guidance available',
    };
  },
});

// Step 2: Search videos based on keywords
const searchVideosStep = createStep({
  id: 'search-videos',
  inputSchema: z.object({
    core: z.array(z.string()),
    adjacent: z.array(z.string()),
    rationale: z.string().optional(),
    targetCreatorDescription: z.string(),
    scoutGuidance: z.string().optional(),
  }),
  outputSchema: z.object({
    videos: z.array(z.any()), // TiktokVideoSchema
    totalVideos: z.number(),
    keywords: z.array(z.string()),
    targetCreatorDescription: z.string(), // Pass through
    scoutGuidance: z.string().optional(), // Pass through
  }),
  execute: async ({ inputData }) => {
    const { core, adjacent, targetCreatorDescription, scoutGuidance } =
      inputData;
    const allKeywords = [...core, ...adjacent];
    console.log('Searching videos for keywords:', allKeywords);

    const allVideos: TiktokVideoSchema[] = [];

    // Search videos for each keyword
    for (const keyword of allKeywords) {
      // Sanitize keyword, remove hashtags and leading/trailing whitespace
      const sanitizedKeyword = keyword.replace(/#/g, '').trim();
      try {
        console.log(`Searching videos for keyword: "${keyword}"`);
        const videos = await videoScouter.searchTiktokVideos(
          sanitizedKeyword,
          0, // offset
          25, // count - get more videos per keyword
          0, // sort_type: relevance
          0, // publish_time: unlimited
        );

        if (videos && videos.length > 0) {
          allVideos.push(...videos);
          console.log(`Found ${videos.length} videos for keyword "${keyword}"`);
        }
      } catch (error) {
        console.error(
          `Error searching videos for keyword "${keyword}":`,
          error,
        );
        // Continue with next keyword
      }
    }

    console.log(`Total videos found: ${allVideos.length}`);

    return {
      videos: allVideos,
      totalVideos: allVideos.length,
      keywords: allKeywords,
      targetCreatorDescription,
      scoutGuidance,
    };
  },
});

// Step 3: Extract unique creators from videos
const extractUniqueCreatorsStep = createStep({
  id: 'extract-unique-creators',
  inputSchema: z.object({
    videos: z.array(z.any()), // TiktokVideoSchema
    totalVideos: z.number(),
    keywords: z.array(z.string()),
    targetCreatorDescription: z.string(),
    scoutGuidance: z.string().optional(),
  }),
  outputSchema: z.object({
    creators: z.array(z.any()), // TiktokVideoAuthorSchema
    totalCreators: z.number(),
    targetCreatorDescription: z.string(), // Pass through
    scoutGuidance: z.string().optional(), // Pass through
  }),
  execute: async ({ inputData }) => {
    const { videos, targetCreatorDescription, scoutGuidance } = inputData;
    console.log(`Extracting unique creators from ${videos.length} videos`);

    // Extract creators and deduplicate by unique_id
    const creatorMap = new Map<string, TiktokVideoAuthorSchema>();

    for (const video of videos) {
      const creator = video.author;
      if (creator && creator.unique_id && !creatorMap.has(creator.unique_id)) {
        creatorMap.set(creator.unique_id, creator);
      }
    }

    const uniqueCreators = Array.from(creatorMap.values());
    console.log(`Found ${uniqueCreators.length} unique creators`);

    return {
      creators: uniqueCreators,
      totalCreators: uniqueCreators.length,
      targetCreatorDescription,
      scoutGuidance,
    };
  },
});

// Step 4: Filter creators using the creator filter agent
const filterCreatorsStep = createStep({
  id: 'filter-creators',
  inputSchema: z.object({
    creators: z.array(z.any()),
    totalCreators: z.number(),
    targetCreatorDescription: z.string(), // Pass through from initial input
    scoutGuidance: z.string().optional(), // Scout's refined guidance
  }),
  outputSchema: workflowOutputSchema,
  execute: async ({ inputData, mastra }) => {
    const { creators, targetCreatorDescription, scoutGuidance } = inputData;
    console.log(`Filtering ${creators.length} creators in batches`);
    console.log('Original requirements:', targetCreatorDescription);
    if (scoutGuidance) {
      console.log('Scout guidance:', scoutGuidance);
    }

    const creatorFilterAgent = mastra?.getAgent('creatorFilterAgent');
    if (!creatorFilterAgent) {
      throw new Error('Creator filter agent not found');
    }

    const batchSize = 100;
    const allQualifiedCreators: Array<{
      unique_id: string;
      collect_reason: string;
    }> = [];

    // Process creators in batches
    for (let i = 0; i < creators.length; i += batchSize) {
      const batch = creators.slice(i, i + batchSize);
      console.log(
        `Processing batch ${Math.floor(i / batchSize) + 1} with ${batch.length} creators`,
      );

      try {
        // Construct enhanced prompt with both original requirements and scout guidance
        let prompt = `Creator Requirements: ${targetCreatorDescription}`;

        if (scoutGuidance) {
          prompt += `\n\nScout Guidance: ${scoutGuidance}`;
        }

        prompt += `\n\nCreator Data: ${JSON.stringify(batch, null, 2)}`;

        const userMessage: CoreMessage = {
          role: 'user',
          content: prompt,
        };

        const resp = await creatorFilterAgent.generate([userMessage]);
        const assistantMessage = resp.response.messages[0];
        const content = (assistantMessage.content as Array<TextPart>)[0];

        // Extract JSON from the response
        const parsedResult = extractFirstJson(content.text);
        const parseResult = creatorFilterSchema.safeParse(parsedResult);

        if (parseResult.success) {
          allQualifiedCreators.push(...parseResult.data.qualified_kols);
          console.log(
            `Batch ${Math.floor(i / batchSize) + 1} yielded ${parseResult.data.qualified_kols.length} qualified creators`,
          );
        } else {
          console.error(
            `Failed to parse response for batch ${Math.floor(i / batchSize) + 1}:`,
            parseResult.error,
          );
        }
      } catch (error) {
        console.error(
          `Error processing batch ${Math.floor(i / batchSize) + 1}:`,
          error,
        );
        // Continue with next batch
      }
    }

    console.log(
      `Total qualified creators found: ${allQualifiedCreators.length}`,
    );

    // Convert to the expected output format
    const results = allQualifiedCreators.map((creator) => ({
      url: `https://www.tiktok.com/@${creator.unique_id}`,
      reason: creator.collect_reason,
    }));

    console.log('results', results);

    return {
      desiredCreators: creators.length,
      scoutedCreators: allQualifiedCreators.length,
      results,
    };
  },
});

// Define the workflow steps and their relationships
creatorScoutWorkflow
  .then(analyzeRequirementStep)
  .then(searchVideosStep)
  .then(extractUniqueCreatorsStep)
  .then(filterCreatorsStep)
  .commit();
