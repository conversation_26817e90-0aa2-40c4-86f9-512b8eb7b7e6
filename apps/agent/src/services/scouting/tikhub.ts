import axios from 'axios';
import { env } from '@/lib/env';
import { TiktokChallengeSchema, TiktokVideoSchema } from '@repo/constants';
import { TiktokServiceSchema } from '@/schemas/tools_schema';

export interface TikHubHashtagSearchResponse {
  code: number;
  router: string;
  params: {
    keyword: string;
    offset: string;
    count: string;
  };
  data: {
    challenge_list: Array<{
      challenge_info: {
        cha_name: string;
        cid: string;
        use_count: number;
        user_count: number;
        view_count: number;
        desc?: string;
        share_info?: {
          share_url?: string;
        };
      };
    }>;
    cursor: number;
    has_more: number;
  };
}

export interface TikHubVideoSearchResponse {
  code: number;
  router: string;
  params: {
    ch_id: string;
    cursor: string;
    count: string;
  };
  data: {
    aweme_list: Array<{
      desc: string;
      create_time: number;
      author: {
        uid: string;
        nickname: string;
        unique_id: string;
        sec_uid: string;
        region: string;
        language: string;
        signature: string;
        aweme_count: number;
        follower_count: number;
        avatar_larger: {
          url_list: string[];
        };
        create_time: number;
        user_tags: string;
        youtube_channel_id: string;
        ins_id: string;
        twitter_id: string;
      };
      statistics: {
        digg_count: number;
        comment_count: number;
        share_count: number;
        play_count: number;
      };
      video: {
        play_addr: {
          url_list: string[];
        };
        cover: {
          url_list: string[];
        };
        duration: number;
      };
      aweme_id: string;
      desc_language: string;
    }>;
    cursor: number;
    has_more: number;
  };
}

export class TikHubService implements TiktokServiceSchema {
  private baseUrl = 'https://api.tikhub.io/api/v1/tiktok';
  private apiKey: string;

  constructor() {
    this.apiKey = env.TIKHUB_API_KEY;
  }

  private getHeaders() {
    return {
      accept: 'application/json',
      Authorization: `Bearer ${this.apiKey}`,
    };
  }

  /**
   * Search for videos on TikTok (direct)
   * @param keyword The hashtag to search for
   * @param offset Pagination offset
   * @param count Number of results to return
   * @param sort_type Sort type (0: relevance, 1: most liked)
   * @param publish_time Publish time (0: unlimited, 1: last 24 hours, 7: past week, 30: past month, 90: past 3 months, 180: past 6 months)
   */
  async searchVideos(
    keyword: string,
    offset = 0,
    count = 20,
    sort_type = 0,
    publish_time = 0,
  ): Promise<TiktokVideoSchema[]> {
    try {
      const response = await axios.get<TikHubVideoSearchResponse>(
        `${this.baseUrl}/app/v3/fetch_video_search_result`,
        {
          headers: this.getHeaders(),
          params: { keyword, offset, count, sort_type, publish_time },
        },
      );

      return response.data.data.aweme_list.map((video) => ({
        title: video.desc,
        hashtags: this.extractHashtags(video.desc),
        description: video.desc,
        platform: 'tiktok',
        video_id: video.aweme_id,
        video_url: video.video?.play_addr?.url_list?.[0] || '',
        thumbnail_url: video.video?.cover?.url_list?.[0] || '',
        publish_time: new Date(video.create_time * 1000).toISOString(),
        duration: video.video?.duration || 0,
        view_count: video.statistics?.play_count || 0,
        like_count: video.statistics?.digg_count || 0,
        comment_count: video.statistics?.comment_count || 0,
        share_count: video.statistics?.share_count || 0,
        author: {
          uid: video.author?.uid,
          nickname: video.author?.nickname,
          unique_id: video.author?.unique_id,
          sec_uid: video.author?.sec_uid,
          region: video.author?.region,
          language: video.author?.language,
          signature: video.author?.signature,
          aweme_count: video.author?.aweme_count,
          follower_count: video.author?.follower_count,
          avatar_url: video.author?.avatar_larger.url_list?.[0] || '',
          create_time: video.author?.create_time,
          user_tags: video.author?.user_tags,
          youtube_channel_id: video.author?.youtube_channel_id,
          ins_id: video.author?.ins_id,
          twitter_id: video.author?.twitter_id,
        },
      }));
    } catch (error) {
      console.error('Error searching videos:', error);
      throw error;
    }
  }

  /**
   * Search for hashtags on TikTok
   * @param keyword The hashtag to search for
   * @param offset Pagination offset
   * @param count Number of results to return
   */
  async searchHashtag(
    keyword: string,
    offset = 0,
    count = 20,
  ): Promise<TiktokChallengeSchema[]> {
    try {
      const response = await axios.get<TikHubHashtagSearchResponse>(
        `${this.baseUrl}/app/v3/fetch_hashtag_search_result`,
        {
          headers: this.getHeaders(),
          params: { keyword, offset, count },
        },
      );

      return response.data.data.challenge_list.map((challenge) => ({
        challenge_id: challenge.challenge_info.cid,
        challenge_name: challenge.challenge_info.cha_name,
        use_count: challenge.challenge_info.use_count,
        user_count: challenge.challenge_info.user_count,
        view_count: challenge.challenge_info.view_count,
      }));
    } catch (error) {
      console.error('Error searching hashtag:', error);
      throw error;
    }
  }

  /**
   * Get videos for a specific hashtag
   * @param challengeId The challenge/hashtag ID
   * @param cursor Pagination cursor
   * @param count Number of results to return
   */
  async getHashtagVideos(
    challengeId: string,
    cursor = 0,
    count = 20,
  ): Promise<TiktokVideoSchema[]> {
    try {
      // Maximum items per page is 25
      const MAX_ITEMS_PER_PAGE = 25;
      let allVideos: TiktokVideoSchema[] = [];
      let currentCursor = cursor;
      let remainingCount = count;

      // Fetch pages until we have enough videos or there are no more results
      while (remainingCount > 0) {
        // Calculate how many items to fetch in this request
        const itemsToFetch = Math.min(remainingCount, MAX_ITEMS_PER_PAGE);

        const response = await axios.get<TikHubVideoSearchResponse>(
          `${this.baseUrl}/app/v3/fetch_hashtag_video_list`,
          {
            headers: this.getHeaders(),
            params: {
              ch_id: challengeId,
              cursor: currentCursor,
              count: itemsToFetch,
            },
          },
        );

        const videos = response.data.data.aweme_list.map((video) => ({
          title: video.desc,
          hashtags: this.extractHashtags(video.desc),
          description: video.desc,
          platform: 'tiktok',
          video_id: video.aweme_id,
          video_url: video.video?.play_addr?.url_list?.[0] || '',
          thumbnail_url: video.video?.cover?.url_list?.[0] || '',
          publish_time: new Date(video.create_time * 1000).toISOString(),
          duration: video.video?.duration || 0,
          view_count: video.statistics?.play_count || 0,
          like_count: video.statistics?.digg_count || 0,
          comment_count: video.statistics?.comment_count || 0,
          share_count: video.statistics?.share_count || 0,
          author: {
            uid: video.author?.uid,
            nickname: video.author?.nickname,
            unique_id: video.author?.unique_id,
            sec_uid: video.author?.sec_uid,
            region: video.author?.region,
            language: video.author?.language,
            signature: video.author?.signature,
            aweme_count: video.author?.aweme_count,
            follower_count: video.author?.follower_count,
            avatar_url: video.author?.avatar_larger.url_list?.[0] || '',
            create_time: video.author?.create_time,
            user_tags: video.author?.user_tags,
            youtube_channel_id: video.author?.youtube_channel_id,
            ins_id: video.author?.ins_id,
            twitter_id: video.author?.twitter_id,
          },
        }));

        allVideos = [...allVideos, ...videos];

        // Update remaining count
        remainingCount -= videos.length;

        // If we didn't get as many videos as requested or there are no more results, break
        if (videos.length < itemsToFetch || !response.data.data.has_more) {
          break;
        }

        // Update cursor for next page
        currentCursor = response.data.data.cursor;
      }

      return allVideos;
    } catch (error) {
      console.error('Error getting hashtag videos:', error);
      throw error;
    }
  }

  /**
   * Extract hashtags from a string
   * @param text Text containing hashtags
   */
  private extractHashtags(text: string): string[] {
    const hashtagRegex = /#(\w+)/g;
    const matches = text.match(hashtagRegex);
    return matches ? matches.map((tag) => tag.substring(1)) : [];
  }
}
