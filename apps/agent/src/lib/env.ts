import * as v from 'valibot';
import * as dotenv from 'dotenv';

dotenv.config();

export const envSchema = v.object({
  DB_POSTGRES_URL: v.string(),
  GOOGLE_GENERATIVE_AI_API_KEY: v.string(),
  TIKHUB_API_KEY: v.string(),
  RAPIDAPI_KEY: v.string(),
  OPENROUTER_API_KEY: v.string(),
  PREFERRED_TIKTOK_SERVICE: v.optional(
    v.union([v.literal('tikhub'), v.literal('tokapi')]),
  ),
});

export type Env = v.InferInput<typeof envSchema>;

let env: Env;
try {
  const parsedEnv = v.parse(envSchema, process.env);
  env = parsedEnv;
} catch (error) {
  throw new Error(`Invalid environment variables: ${error}`);
}

export { env };
