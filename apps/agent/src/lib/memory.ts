import { Memory } from '@mastra/memory';
import { PostgresStore } from '@mastra/pg';
import { env } from './env';
import { google } from '@ai-sdk/google';

const connectionString = env.DB_POSTGRES_URL;

const agentMemory = new Memory({
  options: {
    lastMessages: 50,
    threads: {
      generateTitle: true,
    },
  },
  storage: new PostgresStore({
    connectionString,
  }),
  //
  //   vector: new PgVector({ connectionString }),
  // vector: new PgVector({ connectionString }),
  embedder: google.textEmbeddingModel('gemini-embedding-exp-03-07'),
});

export default agentMemory;
