{"name": "@joaimono/agent", "version": "0.1.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "type": "module", "dependencies": {"@ai-sdk/google": "catalog:", "@inquirer/prompts": "^7.5.1", "@mastra/core": "catalog:", "@mastra/memory": "catalog:", "@mastra/pg": "catalog:", "@neondatabase/serverless": "catalog:", "@openrouter/ai-sdk-provider": "catalog:", "@repo/constants": "workspace:*", "@repo/db": "workspace:*", "ai": "catalog:", "axios": "catalog:", "valibot": "catalog:", "zod": "catalog:"}, "devDependencies": {"@types/node": "catalog:", "dotenv": "^16.5.0", "mastra": "catalog:", "typescript": "catalog:"}}