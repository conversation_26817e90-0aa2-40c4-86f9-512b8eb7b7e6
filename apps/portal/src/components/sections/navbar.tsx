'use client'

import { useEffect, useRef, useState } from 'react'
import { AnimatePresence, motion } from 'motion/react'
import { Link, useRouter } from '@tanstack/react-router'
import { LogOut, Menu, X } from 'lucide-react'
import type { NavBarData, UserProfile } from '@/services/site.api'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { NavMenu } from '@/components/nav-menu'
import { ThemeSwitcher } from '@/components/theme-switcher'
import { cn } from '@/lib/utils'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { AuthAvatar } from '../auth-avatar'
import { signOut } from '@/lib/auth-client'

const INITIAL_WIDTH = '70rem'
const MAX_WIDTH = '800px'

// Animation variants
const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 },
}

const drawerVariants = {
  hidden: { opacity: 0, y: 100 },
  visible: {
    opacity: 1,
    y: 0,
    rotate: 0,
    transition: {
      type: 'spring',
      damping: 15,
      stiffness: 200,
      staggerChildren: 0.03,
    },
  },
  exit: {
    opacity: 0,
    y: 100,
    transition: { duration: 0.1 },
  },
}

const drawerMenuContainerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
}

const drawerMenuVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
}

export interface NavbarProps {
  data: NavBarData
  user: UserProfile | null
}

export function Navbar({ ...props }: NavbarProps) {
  // console.log('Navbar component rendering')
  const data = props.data
  const user = props.user
  const navbarRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  // No longer using motion's useScroll hook directly
  const [hasScrolled, setHasScrolled] = useState(false)
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [activeSection, setActiveSection] = useState('hero')

  useEffect(() => {
    const handleScroll = () => {
      const sections = data.links.map((item) => item.url.substring(1))

      for (const section of sections) {
        const element = document.getElementById(section)
        if (element) {
          const rect = element.getBoundingClientRect()
          if (rect.top <= 150 && rect.bottom >= 150) {
            setActiveSection(section)
            break
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll()

    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    // console.log('Setting up scroll detection with Intersection Observer')

    // Create a sentinel element for scroll detection
    const sentinelEl = document.createElement('div')
    sentinelEl.style.position = 'absolute'
    sentinelEl.style.top = '5px' // Detect scrolling past 5px (more sensitive)
    sentinelEl.style.left = '0'
    sentinelEl.style.width = '100%'
    sentinelEl.style.height = '1px'
    sentinelEl.style.pointerEvents = 'none'
    sentinelEl.style.zIndex = '-1' // Make sure it's behind other elements
    document.body.appendChild(sentinelEl)
    // console.log('Sentinel element created and added to body')

    // Create the observer
    const observer = new IntersectionObserver(
      (entries) => {
        const isIntersecting = entries[0].isIntersecting
        // console.log('Sentinel intersection:', isIntersecting)
        // console.log('Current hasScrolled state:', hasScrolled)

        // When sentinel is not intersecting, we've scrolled down
        const shouldHaveScrolled = !isIntersecting

        if (hasScrolled !== shouldHaveScrolled) {
          // console.log('Updating hasScrolled to:', shouldHaveScrolled)
          setHasScrolled(shouldHaveScrolled)
        }
      },
      {
        threshold: [0, 0.1, 0.5, 1.0], // Multiple thresholds for better sensitivity
        rootMargin: '-5px 0px 0px 0px', // Consider element intersecting only when fully visible
      },
    )

    // Start observing
    observer.observe(sentinelEl)

    // Fallback: Also add a standard scroll listener in case IntersectionObserver doesn't work
    const handleScroll = () => {
      const scrollY = window.scrollY || window.pageYOffset
      // console.log('Fallback scroll handler, scrollY:', scrollY)
      if (scrollY > 5 && !hasScrolled) {
        // console.log('Fallback setting hasScrolled to true')
        setHasScrolled(true)
      } else if (scrollY <= 5 && hasScrolled) {
        // console.log('Fallback setting hasScrolled to false')
        setHasScrolled(false)
      }
    }

    window.addEventListener('scroll', handleScroll)
    // Initial check
    handleScroll()

    // Cleanup
    return () => {
      // console.log('Cleaning up Intersection Observer and scroll listener')
      observer.disconnect()
      document.body.removeChild(sentinelEl)
      window.removeEventListener('scroll', handleScroll)
    }
  }, [hasScrolled])

  const toggleDrawer = () => setIsDrawerOpen((prev) => !prev)
  const handleOverlayClick = () => setIsDrawerOpen(false)

  async function handleLogout(): Promise<void> {
    // remove session token from cookie
    try {
      // const { refresh_token, access_token } = await cmsClient.refresh()
      // window.location.reload()
      const resp = await signOut()
      console.log('logout resp', resp)
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <header
      ref={navbarRef}
      className={cn(
        'sticky z-50 mx-4 flex justify-center transition-all duration-300 md:mx-0',
        hasScrolled ? 'top-6' : 'top-4 mx-0',
      )}
    >
      {/* Log rendering state */}
      {(() => {
        // console.log('Rendering motion.div with hasScrolled:', hasScrolled)
        return null
      })()}
      <motion.div
        initial={{ width: INITIAL_WIDTH }}
        animate={{ width: hasScrolled ? MAX_WIDTH : INITIAL_WIDTH }}
        transition={{ duration: 0.3, ease: [0.25, 0.1, 0.25, 1] }}
        // onAnimationStart={() =>
        //   console.log('Animation started, hasScrolled:', hasScrolled)
        // }
        // onAnimationComplete={() =>
        //   console.log(
        //     'Animation completed, width:',
        //     hasScrolled ? MAX_WIDTH : INITIAL_WIDTH,
        //   )
        // }
      >
        {(() => {
          // console.log(
          //   'Navbar inner div classes:',
          //   classes,
          //   'hasScrolled:',
          //   hasScrolled,
          // )
          return null
        })()}
        <div
          className={cn(
            'mx-auto max-w-7xl rounded-2xl transition-all duration-300  xl:px-0',
            hasScrolled
              ? 'px-2 border border-border backdrop-blur-lg bg-background/75'
              : 'shadow-none px-7',
          )}
        >
          <div className="flex h-[56px] items-center justify-between p-4">
            <Link href="/" className="flex items-center gap-3" to={'.'}>
              <Icons.logo className="size-7 md:size-10" />
              <p className="text-lg font-semibold text-primary">JellyOtter</p>
            </Link>

            <NavMenu data={data.links} />

            <div className="flex flex-row items-center gap-1 md:gap-3 shrink-0">
              <div className="flex items-center space-x-6">
                {!user?.id && (
                  <Link
                    className="bg-secondary h-8 hidden md:flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-fit px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12]"
                    to={'/signin'}
                  >
                    Try for free
                  </Link>
                )}
                {user?.id &&
                  AuthAvatar({
                    user,
                    onLogout: handleLogout,
                  })}
              </div>
              <ThemeSwitcher />
              <button
                className="md:hidden border border-border size-8 rounded-md cursor-pointer flex items-center justify-center"
                onClick={toggleDrawer}
              >
                {isDrawerOpen ? (
                  <X className="size-5" />
                ) : (
                  <Menu className="size-5" />
                )}
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Mobile Drawer */}
      <AnimatePresence>
        {isDrawerOpen && (
          <>
            <motion.div
              className="fixed inset-0 bg-black/50 backdrop-blur-sm"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={overlayVariants}
              transition={{ duration: 0.2 }}
              onClick={handleOverlayClick}
            />

            <motion.div
              className="fixed inset-x-0 w-[95%] mx-auto bottom-3 bg-background border border-border p-4 rounded-xl shadow-lg"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={drawerVariants}
            >
              {/* Mobile menu content */}
              <div className="flex flex-col gap-4">
                <div className="flex items-center justify-between">
                  <Link className="flex items-center gap-3" to={'/'}>
                    <Icons.logo className="size-7 md:size-10" />
                    <p className="text-lg font-semibold text-primary">
                      Jelly Otter
                    </p>
                  </Link>
                  <button
                    onClick={toggleDrawer}
                    className="border border-border rounded-md p-1 cursor-pointer"
                  >
                    <X className="size-5" />
                  </button>
                </div>

                <motion.ul
                  className="flex flex-col text-sm mb-4 border border-border rounded-md"
                  variants={drawerMenuContainerVariants}
                >
                  <AnimatePresence>
                    {data.links.map((item) => (
                      <motion.li
                        key={item.id}
                        className="p-2.5 border-b border-border last:border-b-0"
                        variants={drawerMenuVariants}
                      >
                        <a
                          href={item.url}
                          onClick={(e) => {
                            e.preventDefault()
                            const element = document.getElementById(
                              item.url.substring(1),
                            )
                            element?.scrollIntoView({ behavior: 'smooth' })
                            setIsDrawerOpen(false)
                          }}
                          className={`underline-offset-4 hover:text-primary/80 transition-colors ${
                            activeSection === item.url.substring(1)
                              ? 'text-primary font-medium'
                              : 'text-primary/60'
                          }`}
                        >
                          {item.name}
                        </a>
                      </motion.li>
                    ))}
                  </AnimatePresence>
                </motion.ul>

                {/* Action buttons */}
                <div className="flex flex-col gap-2">
                  {!user && (
                    <Link
                      className="bg-secondary h-8 flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-full px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12] hover:bg-secondary/80 transition-all ease-out active:scale-95"
                      to={'/signin'}
                    >
                      Try for free
                    </Link>
                  )}

                  {user && (
                    <div className="flex flex-col gap-3 border border-border rounded-md p-3">
                      <div className="flex items-center gap-2">
                        <Avatar>
                          {user.image && (
                            <AvatarImage
                              src={user.image}
                              alt={user.name || 'User'}
                            />
                          )}
                          <AvatarFallback>
                            {user.name
                              ? user.name.charAt(0).toUpperCase()
                              : 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex flex-col">
                          <span className="text-sm font-medium">
                            {user.name}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {user.email}
                          </span>
                        </div>
                      </div>

                      <div className="flex flex-col gap-2">
                        <Button
                          variant="destructive"
                          className="justify-start"
                          size="sm"
                          onClick={() => handleLogout}
                        >
                          <LogOut className="mr-2 h-4 w-4" />
                          Log out
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </header>
  )
}
