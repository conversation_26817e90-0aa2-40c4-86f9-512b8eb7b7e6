import {
  Link,
  Outlet,
  createFileRoute,
  redirect,
  useRouter,
} from '@tanstack/react-router'
import { getUserFn } from '@/services/auth.api'
import { Icons } from '@/components/icons'
import { AuthAvatar } from '@/components/auth-avatar'
import { Button } from '@/components/ui/button'

export const Route = createFileRoute('/_dashboard')({
  component: DashboardLayout,
  beforeLoad: async () => {
    const user = await getUserFn()
    return { user }
  },
  loader: ({ context }) => {
    const { user } = context
    // console.log('user', user)
    if (!user.id) {
      // Redirect to the sign-in page if the user is not authenticated
      throw redirect({ to: '/signin' })
    }
    return { user }
  },
})

function DashboardLayout() {
  const { user } = Route.useLoaderData()
  const router = useRouter()

  return (
    <div className="min-h-screen bg-background">
      {/* Header Nav Bar */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
        <div className="flex h-14 items-center justify-between px-4">
          <div className="flex items-center gap-2">
            {/* <Link to="/" className="flex items-center gap-2"> */}
            <Button
              className="flex items-center gap-2"
              onClick={() => router.history.back()}
              variant={'ghost'}
            >
              <Icons.logo className="h-8 w-8" />
              <span className="font-semibold text-lg">JellyOtter</span>
            </Button>
            {/* </Link> */}
          </div>

          <div className="flex items-center gap-4">
            <AuthAvatar user={user} />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        <div className="py-4 px-4">
          <Outlet />
        </div>
      </main>
    </div>
  )
}
