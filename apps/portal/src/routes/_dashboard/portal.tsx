import { Link, createFileRoute } from '@tanstack/react-router'
import { useSuspenseQuery } from '@tanstack/react-query'
import { portalQueries } from '@/services/queries'
import { CreateWorkflowDialog } from '@/components/workflows/create-workflow-dialog'
import { StatusBadge } from '@/components/status-badge'

// Define types for workflow data
interface UserWorkflow {
  id: number
  userId: string
  workflowRunId: string
  workflowName: string
  status: string
  createdAt: Date
  updatedAt: Date
}

interface WorkflowUI {
  id: string
  title: string
  icon: string
  status: string
}

export const Route = createFileRoute('/_dashboard/portal')({
  component: PortalPage,
  loader: async ({ context }) => {
    const queryClient = context.queryClient
    await queryClient.prefetchQuery(portalQueries.userWorkflows())
    return {}
  },
})

function PortalPage() {
  // Fetch user workflows
  const {
    data: userWorkflows,
    isError,
    error,
  } = useSuspenseQuery(portalQueries.userWorkflows())

  // Map the workflow data to the UI format
  const workflows: WorkflowUI[] = Array.isArray(userWorkflows)
    ? userWorkflows.map((workflow: UserWorkflow) => ({
        id: workflow.workflowRunId,
        title: workflow.workflowName,
        icon: getWorkflowIcon(workflow.workflowName),
        status: workflow.status,
      }))
    : []

  // Helper function to get an icon based on workflow name
  function getWorkflowIcon(workflowName: string): string {
    const icons: Record<string, string> = {
      aiScriptWorkflow: '📝',
      default: '🔄',
    }
    return icons[workflowName] || icons.default
  }

  // Show error if there was a problem fetching workflows
  if (isError) {
    console.error('Error fetching workflows:', error)
  }

  return (
    <>
      {/* Top Navigation */}
      <div className="flex items-center justify-between mb-4 px-2">
        <span className="text-2xl font-medium">My Workflows</span>
        <CreateWorkflowDialog />
      </div>
      {/* Workflow Grid */}
      {workflows.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <div className="text-4xl mb-4">🔍</div>
          <h3 className="text-xl font-medium mb-2">No workflows found</h3>
          <p className="text-gray-500 mb-4">
            You haven't created any workflows yet.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {workflows.map((workflow) => (
            <Link
              key={workflow.id}
              className="border rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
              to="/workflows/$wid"
              params={{ wid: workflow.id }}
            >
              <div className="flex flex-col h-full">
                <div className="flex items-start justify-between mb-16">
                  <div className="text-4xl">
                    <svg
                      width="64"
                      height="64"
                      viewBox="0 0 64 64"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M32 56C45.2548 56 56 45.2548 56 32C56 18.7452 45.2548 8 32 8C18.7452 8 8 18.7452 8 32C8 45.2548 18.7452 56 32 56Z"
                        stroke="#E5E7EB"
                        strokeWidth="2"
                      />
                      <path
                        d="M32 48C40.8366 48 48 40.8366 48 32C48 23.1634 40.8366 16 32 16C23.1634 16 16 23.1634 16 32C16 40.8366 23.1634 48 32 48Z"
                        stroke="#E5E7EB"
                        strokeWidth="2"
                      />
                      <path
                        d="M32 40C36.4183 40 40 36.4183 40 32C40 27.5817 36.4183 24 32 24C27.5817 24 24 27.5817 24 32C24 36.4183 27.5817 40 32 40Z"
                        fill="#E5E7EB"
                      />
                    </svg>
                  </div>
                  <Link
                    className="text-gray-400 hover:text-gray-600"
                    to="/workflows/$wid"
                    params={{ wid: workflow.id }}
                  >
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M5 10C3.9 10 3 10.9 3 12C3 13.1 3.9 14 5 14C6.1 14 7 13.1 7 12C7 10.9 6.1 10 5 10Z"
                        stroke="currentColor"
                        strokeWidth="1.5"
                      />
                      <path
                        d="M19 10C17.9 10 17 10.9 17 12C17 13.1 17.9 14 19 14C20.1 14 21 13.1 21 12C21 10.9 20.1 10 19 10Z"
                        stroke="currentColor"
                        strokeWidth="1.5"
                      />
                      <path
                        d="M12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10Z"
                        stroke="currentColor"
                        strokeWidth="1.5"
                      />
                    </svg>
                  </Link>
                </div>
                <div>
                  <h3 className="font-medium mb-2">{workflow.title}</h3>
                  <StatusBadge
                    status={
                      workflow.status === 'completed'
                        ? 'success'
                        : workflow.status
                    }
                  />
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}
    </>
  )
}
