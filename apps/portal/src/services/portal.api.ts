import { createServerFn } from '@tanstack/react-start'
import {
  createWorkflowAndRun,
  getUserWorkflows,
  getWorkflowSnapshot,
} from '@/lib/agent'
import { authMiddleware } from '@/lib/middlewares/auth-middleware'
import * as v from 'valibot'
import { WorkflowNames } from '@repo/constants'
import type { WorkflowRunResult } from '@mastra/client-js'
import { getWorkflowContexts } from '@/lib/agent/index'
import { authValidation } from '@/services/shared'

const createWorkflowSchema = v.object({
  workflowType: v.enum(WorkflowNames),
  triggerData: v.any(),
})

const createWorkflowAndRunSchema = v.object({
  workflowRunId: v.string(),
})

const getWorkflowSnapshotSchema = v.object({
  workflowRunId: v.string(),
})

export const getUserWorkflowsFn = createServerFn()
  .middleware([authMiddleware])
  .handler(async ({ context }) => {
    const user = authValidation(context)

    const workflows = await getUserWorkflows(user.id)
    return workflows
  })

export const getWorkflowContextsFn = createServerFn()
  .middleware([authMiddleware])
  .validator(createWorkflowAndRunSchema)
  .handler(async ({ context, data }) => {
    authValidation(context)

    const { workflowRunId } = data
    const contexts = await getWorkflowContexts(workflowRunId)
    return contexts
  })

export const getWorkflowSnapshotFn = createServerFn()
  .middleware([authMiddleware])
  .validator(getWorkflowSnapshotSchema)
  .handler(async ({ context, data }) => {
    authValidation(context)

    const { workflowRunId } = data
    const snapshot = await getWorkflowSnapshot(workflowRunId)
    return snapshot
  })

export const createWorkflowFn = createServerFn()
  .middleware([authMiddleware])
  .validator(createWorkflowSchema)
  .handler(async ({ context, data }) => {
    const user = authValidation(context)

    const { workflowType, triggerData } = data

    const result = await createWorkflowAndRun(
      user.id,
      workflowType,
      triggerData,
      onRecord,
    )
    return result
  })

// Serverside only
function onRecord(record: WorkflowRunResult): void {
  // TODO: emit a websocket event to notify the client
  console.log('Workflow record:', record)
}
