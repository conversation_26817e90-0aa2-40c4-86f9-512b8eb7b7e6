'use server'

import * as v from 'valibot'

// Define the schema for server environment variables
const serverSchema = v.object({
  // Make all environment variables optional during build
  DB_POSTGRES_URL: v.pipe(v.string(), v.minLength(1), v.url()),
  ADMIN_PANEL_URL: v.pipe(v.string(), v.minLength(1), v.url()),
  BETTER_AUTH_SECRET: v.pipe(v.string(), v.minLength(1)),
  BETTER_AUTH_URL: v.pipe(v.string(), v.minLength(1), v.url()),
  GITHUB_CLIENT_ID: v.pipe(v.string(), v.minLength(1)),
  GITHUB_CLIENT_SECRET: v.pipe(v.string(), v.minLength(1)),
  GOOGLE_CLIENT_ID: v.pipe(v.string(), v.minLength(1)),
  GOOGLE_CLIENT_SECRET: v.pipe(v.string(), v.min<PERSON>ength(1)),
  MASTRA_API_URL: v.pipe(v.string(), v.minLength(1), v.url()),
  WS_SERVER_URL: v.pipe(v.string(), v.minLength(1), v.url()),
})

// Create a safe version of the environment
let processedEnv: v.InferInput<typeof serverSchema>

// console.log(process.env)

try {
  processedEnv = v.parse(serverSchema, process.env)
} catch (error) {
  console.error('❌ Invalid environment variables:', error)
  throw error
}

export const env = processedEnv
