{"name": "@joaimono/portal", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "APP_NAME=portal NODE_ENV=development vinxi dev", "start": "APP_NAME=portal NODE_ENV=production vinxi start", "build": "NODE_ENV=production vinxi build", "serve": "APP_NAME=portal vite preview", "test": "APP_NAME=portal vitest run", "lint": "eslint", "lint:fix": "eslint --fix", "format": "prettier", "check": "prettier --write . && eslint --fix"}, "dependencies": {"@mastra/client-js": "catalog:", "@radix-ui/react-accordion": "catalog:", "@radix-ui/react-avatar": "catalog:", "@radix-ui/react-dialog": "catalog:", "@radix-ui/react-dropdown-menu": "catalog:", "@radix-ui/react-label": "catalog:", "@radix-ui/react-slot": "catalog:", "@repo/auth": "workspace:*", "@repo/constants": "workspace:*", "@repo/db": "workspace:*", "@repo/ui": "workspace:*", "@tailwindcss/vite": "catalog:", "@tanstack/react-query": "catalog:", "@tanstack/react-query-devtools": "catalog:", "@tanstack/react-router": "catalog:", "@tanstack/react-router-devtools": "catalog:", "@tanstack/react-router-with-query": "catalog:", "@tanstack/react-start": "catalog:", "@tanstack/router-plugin": "catalog:", "class-variance-authority": "catalog:", "clsx": "catalog:", "cobe": "catalog:", "color-bits": "catalog:", "lucide-react": "catalog:", "motion": "catalog:", "pg": "catalog:", "react": "catalog:", "react-dom": "catalog:", "react-hot-toast": "^2.5.2", "react-icons": "catalog:", "tailwind-merge": "catalog:", "tailwindcss": "catalog:", "tailwindcss-animate": "catalog:", "valibot": "catalog:", "vinxi": "catalog:", "vite-tsconfig-paths": "catalog:"}, "devDependencies": {"@tanstack/eslint-config": "catalog:", "@testing-library/dom": "catalog:", "@testing-library/react": "catalog:", "@types/pg": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@vitejs/plugin-react": "catalog:", "jsdom": "catalog:", "prettier": "catalog:", "typescript": "catalog:", "vite": "catalog:", "vitest": "catalog:", "web-vitals": "catalog:"}}